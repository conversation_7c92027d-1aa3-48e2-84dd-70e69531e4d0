* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-size: 100%;
    -webkit-overflow-scrolling: touch;
}

html {
    font-size: 33px;
}

html,
body,
#app {
    width: 100%;
    height: 100%;
    overflow: hidden;
}


/* 强制图片不超过容器宽度 */
img {
   max-width: 100%;
   height: auto;
   display: block; /* 避免 inline 元素的默认间隙 */
}

/* 强制按钮无默认样式 */
button {
   border: none;
   outline: none;
   background: none;
   cursor: pointer;
}

/* 媒体查询：手机端恢复默认宽度（取消 PC 端限制） */
@media (max-width: 768px) {
   #app {
       width: 100%;
       height: 100vh;
       max-width: 100%; /* 手机端取消宽度限制 */
       padding: 0 0rem; /* 可选：手机端内边距 */
   }
}

.mobilePage {
   position: relative;
   height: 100vh; /* 使用vh而不是%确保高度 */
   min-height: 100vh; /* 确保最小高度 */
   overflow: hidden; /* 防止内容溢出 */
}

/* .mobilePage {
   position: relative;
   height: 100%;
} */

.mobilePage .logo {
   height: 1.50rem;
   position: absolute;
   left: 0.47rem;
   top: 0.44rem;
   z-index: 99;
}

.mobilePage .page1,
.mobilePage .page2,
.mobilePage .page3,
.mobilePage .page4  {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.mobilePage .page1 .bg,
.mobilePage .page2 .bg,
.mobilePage .page3 .bg,
.mobilePage .page4 .bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片比例并覆盖整个容器 */
}



.mobilePage .action {
   width: 100%;
   position: absolute;
   bottom: 4rem;
   z-index: 9999;
   -webkit-animation-delay: 1s;
   animation-delay: 1s;
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-orient: vertical;
   -webkit-box-direction: normal;
   -ms-flex-direction: column;
   flex-direction: column;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
}

.mobilePage .action .text1 {
   width: 7.8rem;
   margin-bottom: 0.55rem;
}

.mobilePage .action .text2 {
   width: 9.21rem;
   margin-top: 0.45rem;
}

.mobilePage .action .btnBox {
   width: 100%;
   padding: 0 0.2rem;
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -ms-flex-pack: distribute;
   justify-content: space-around;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
}

.mobilePage .action .btnBox .btn {
   width: 6rem;
}

.mobilePage .action .btnBox .btn img {
   width: 100%;
}

.mobilePage .action .btnBox .btn:not(:first-child) {
   margin-left: 0.2rem;
}



.mobilePage .kefu {
   height: 1.18rem;
   position: absolute;
   right: 0.47rem;
   top: 0.64rem;
   z-index: 99;
   width: 3rem;
   height: 1rem;
}