// 1. 处理样式部分

// CSS加载函数
function insertLink(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = window.global_cdn+href;
    document.head.appendChild(link);
}
insertLink('/static/css/main.css');
insertLink('/static/css/swiper.css');
// 2. 处理HTML内容
// 插入主体HTML
document.body.insertAdjacentHTML('afterbegin', `<body>
    <div class="app" id="app">
      <div class="mobilePage" id="copy">
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div class="page1">
                <img
                  alt=""
                  class="bg"
                  src="${window.global_cdn}/static/img/01.js"
                />
              </div>
            </div>
            <div class="swiper-slide">
              <div class="page2">
                <img
                  alt=""
                  class="bg"
                  src="${window.global_cdn}/static/img/02.js"
                />
              </div>
            </div>
            <div class="swiper-slide">
              <div class="page3">
                <img
                  alt=""
                  class="bg"
                  src="${window.global_cdn}/static/img/03.js"
                />
              </div>
            </div>
            <div class="swiper-slide">
              <div class="page4">
                <img
                  alt=""
                  class="bg"
                  src="${window.global_cdn}/static/img/04.js"
                />
              </div>
            </div>
            <span
              aria-atomic="true"
              aria-live="assertive"
              class="swiper-notification"
            ></span>
          </div>
          <div class="action" style="display: none">
            <img
              alt=""
              class="text1"
             
            />
            <div class="btnBox">
              <button class="btn btn1">
                <img
                  class="androidBtn copy abcdef"
                  alt=""
                />
              </button>
            </div>
            <img
              alt=""
              class="text2"
            />
          </div>
        </div>
      </div>
    </div>




</body>`);

// 3. 处理脚本部分

// 脚本加载器
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = window.global_cdn+src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 加载外部脚本
loadScript('/static/js/jquery-3.5.1.min.js')
    .then(() => console.log('/static/js/jquery-3.5.1.min.js 加载成功'))
    .catch(err => console.error('/static/js/jquery-3.5.1.min.js 加载失败', err));
loadScript('/static/js/swiper-bundle.min.js')
    .then(() => console.log('static/js/swiper-bundle.min.js 加载成功'))
    .catch(err => console.error('static/js/swiper-bundle.min.js 加载失败', err));
loadScript('/static/js/appinstall.js')
    .then(() => console.log('/static/js/appinstall.js 加载成功'))
    .catch(err => console.error('/static/js/appinstall.js 加载失败', err));

// 执行内联脚本
    
    
                function swiper(){
                    var swiper = new Swiper(".swiper-container", {
                    loop: true, // 循环模式选项
                    autoplay: {
                        delay: 2500, //1秒切换一次
                        },
                    });
    
                    setTimeout(function() {
                        $(".action").slideToggle(1000);
                    }, 1000);
    
                    var a;
                
                }
    
    
    
                const install = () => {
                console.log('install function called');
                console.log('window.AppInstall:', window.AppInstall);

                const data = window.AppInstall ? window.AppInstall.parseUrlParams() : {};
                console.log('parsed URL params:', data);

                new window.AppInstall({
                        appKey: opkey,
                        channelCode: opchannelCode,
                        server: op_service,
                        onready: function() {
                            console.log('AppInstall ready');
                            const m = this;
                            window.$('body').on('click', '.abcdef', function() {
                                console.log('Download button clicked');
                                m.install();
                                return false;
                            });
                        },
                    },
                    data
                );

                const autoDownload = data.n;
                console.log('autoDownload value:', autoDownload);
                console.log('downtime:', downtime);

                if (autoDownload === undefined) {
                    console.log('Setting up auto download timer for', downtime, 'ms');
                    // 确保在下载按钮显示后再触发自动下载
                    // .action div 在1秒后开始显示，slideToggle需要1秒动画时间，所以至少需要2.5秒
                    const autoDownloadDelay = Math.max(downtime, 2500);
                    setTimeout(() => {
                        console.log('Auto download timer triggered');
                        // 优先选择下载按钮，如果没有则选择客服按钮
                        let clickableElement = document.querySelector('.androidBtn.abcdef');
                        if (!clickableElement || !isElementVisible(clickableElement)) {
                            clickableElement = document.querySelector('.abcdef');
                        }
                        console.log('Found clickable element:', clickableElement);
                        if (clickableElement && isElementVisible(clickableElement)) {
                            console.log('Triggering auto download');
                            clickableElement.click();
                        } else {
                            console.log('No visible clickable element found');
                        }
                    }, autoDownloadDelay);
                } else {
                    console.log('Auto download disabled due to n parameter:', autoDownload);
                }

                // 辅助函数：检查元素是否可见
                function isElementVisible(element) {
                    if (!element) return false;
                    const style = window.getComputedStyle(element);
                    const parentStyle = element.parentElement ? window.getComputedStyle(element.parentElement) : null;
                    return style.display !== 'none' &&
                           style.visibility !== 'hidden' &&
                           style.opacity !== '0' &&
                           (!parentStyle || (parentStyle.display !== 'none' && parentStyle.visibility !== 'hidden'));
                }
            };
    
    
    
            function initApp() {
                if (document.readyState === "complete") {
                        swiper()
                        install(); // 直接执行
                    } else {
                    console.log('等待加载');
                        window.addEventListener("load", swiper); // 等待 load 事件
                        window.addEventListener("load", install); // 等待 load 事件
                }
            }
    
            initApp();
    
        

// 4. 完成初始化
console.log('所有资源加载完成');